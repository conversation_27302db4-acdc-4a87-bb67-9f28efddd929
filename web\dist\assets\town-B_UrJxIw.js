import{d as H,h as a,ab as V,ac as O,ad as y}from"./index-D24a7sNI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 2H7.44237L11 5.91339V2H16.4684L22 8.63795V22H2V2ZM13 4V20H20V9.36205L15.5316 4H13ZM11 20V8.88661L6.55763 4H4V20H11ZM6 7.99805H8.00391V10.002H6V7.99805ZM15 7.99805H17.0039V10.002H15V7.99805ZM6 11.998H8.00391V14.002H6V11.998ZM15 11.998H17.0039V14.002H15V11.998ZM6 15.998H8.00391V18.002H6V15.998ZM15 15.998H17.0039V18.002H15V15.998Z"}}]},b=H({name:"TownIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=V(r),p=a(()=>["t-icon","t-icon-town",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(d,v.value)}});export{b as default};
