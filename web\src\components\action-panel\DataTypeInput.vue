<template>
  <div class="data-type-input">
    <!-- 文本类型 -->
    <t-textarea
      v-if="isTextType"
      v-model="inputValue"
      :placeholder="placeholder"
      :autosize="{ minRows: 5, maxRows: 12 }"
      @change="onInputChange"
    />

    <!-- 数值类型 -->
    <t-input-number
      v-else-if="isNumberType"
      v-model="inputValue"
      :placeholder="placeholder"
      :decimal-places="getDecimalPlaces()"
      :min="getMinValue()"
      :max="getMaxValue()"
      @change="onInputChange"
    />

    <!-- 日期类型 -->
    <t-date-picker
      v-else-if="isDateType"
      v-model="inputValue"
      :placeholder="placeholder"
      format="YYYY-MM-DD"
      @change="onInputChange"
    />

    <!-- 日期时间类型 -->
    <t-date-picker
      v-else-if="isDateTimeType"
      v-model="inputValue"
      :placeholder="placeholder"
      enable-time-picker
      format="YYYY-MM-DD HH:mm:ss"
      @change="onInputChange"
    />

    <!-- 布尔值类型 -->
    <t-switch v-else-if="isBoolType" v-model="inputValue" @change="onInputChange" />

    <!-- 字符类型 -->
    <t-input
      v-else-if="isCharType"
      v-model="inputValue"
      :placeholder="placeholder"
      :maxlength="1"
      @change="onInputChange"
    />

    <!-- 二进制数据类型 -->
    <t-upload
      v-else-if="isBinaryType"
      v-model="fileList"
      :action="uploadAction"
      :before-upload="beforeUpload"
      :on-success="onUploadSuccess"
      :on-error="onUploadError"
      theme="file"
      :placeholder="placeholder || '选择文件'"
      :tips="'支持上传二进制文件'"
    />

    <!-- 对象/数组类型 -->
    <structured-editor
      v-else-if="isObjectOrArrayType"
      v-model="inputValue"
      :data-type="props.dataType as 'array' | 'object'"
      @change="onInputChange"
    />

    <!-- 默认文本输入 -->
    <t-textarea
      v-else
      v-model="inputValue"
      :placeholder="placeholder"
      :autosize="{ minRows: 5, maxRows: 12 }"
      @change="onInputChange"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'DataTypeInput',
};
</script>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import StructuredEditor from './StructuredEditor.vue';

interface Props {
  modelValue?: any;
  dataType?: string;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  dataType: 'string',
  placeholder: '请输入内容',
});

const emits = defineEmits(['update:modelValue', 'change']);

// 内部值
const inputValue = ref(props.modelValue);
const jsonString = ref('');
const jsonError = ref('');
const fileList = ref([]);

// 标记是否是初始化阶段，避免在组件初始化时重置值
const isInitialized = ref(false);

// 注意：结构化编辑器相关代码已移至 StructuredEditor.vue 组件

// 类型判断
const isTextType = computed(() => {
  return ['string'].includes(props.dataType);
});

const isNumberType = computed(() => {
  return ['decimal', 'int', 'short', 'long', 'double', 'float'].includes(props.dataType);
});

const isDateTimeType = computed(() => {
  return props.dataType === 'DateTime';
});

const isDateType = computed(() => {
  return props.dataType === 'Date';
});

const isBoolType = computed(() => {
  return props.dataType === 'bool';
});

const isCharType = computed(() => {
  return props.dataType === 'char';
});

const isBinaryType = computed(() => {
  return props.dataType === 'byte[]';
});

const isObjectOrArrayType = computed(() => {
  return ['object', 'array'].includes(props.dataType);
});

// 数值类型配置
const getDecimalPlaces = () => {
  switch (props.dataType) {
    case 'decimal':
    case 'double':
    case 'float':
      return 2;
    default:
      return 0;
  }
};

const getMinValue = () => {
  switch (props.dataType) {
    case 'short':
      return -32768;
    case 'int':
      return -2147483648;
    case 'long':
      return Number.MIN_SAFE_INTEGER;
    default:
      return undefined;
  }
};

const getMaxValue = () => {
  switch (props.dataType) {
    case 'short':
      return 32767;
    case 'int':
      return 2147483647;
    case 'long':
      return Number.MAX_SAFE_INTEGER;
    default:
      return undefined;
  }
};

// JSON 相关
const getJsonPlaceholder = () => {
  if (props.dataType === 'object') {
    return '请输入JSON对象，例如：\n{\n  "key": "value",\n  "number": 123\n}';
  } else if (props.dataType === 'array') {
    return '请输入JSON数组，例如：\n[\n  "item1",\n  "item2",\n  123\n]';
  }
  return '请输入JSON格式数据';
};

const validateJson = () => {
  if (!jsonString.value.trim()) {
    jsonError.value = '';
    return;
  }

  try {
    const parsed = JSON.parse(jsonString.value);

    if (props.dataType === 'object' && (typeof parsed !== 'object' || Array.isArray(parsed))) {
      jsonError.value = '请输入有效的JSON对象';
      return;
    }

    if (props.dataType === 'array' && !Array.isArray(parsed)) {
      jsonError.value = '请输入有效的JSON数组';
      return;
    }

    jsonError.value = '';
    inputValue.value = parsed;
    onInputChange();
  } catch (error) {
    jsonError.value = 'JSON格式错误，请检查语法';
  }
};

const onJsonChange = () => {
  // 延迟验证，避免输入过程中频繁报错
  setTimeout(validateJson, 500);
};

// 文件上传相关
const uploadAction = '/api/upload'; // 根据实际情况修改
const beforeUpload = (file: File) => {
  // 可以在这里添加文件类型和大小验证
  return true;
};

const onUploadSuccess = (response: any) => {
  // 处理上传成功
  inputValue.value = response.data; // 假设返回文件路径或base64
  onInputChange();
  MessagePlugin.success('文件上传成功');
};

const onUploadError = (error: any) => {
  MessagePlugin.error('文件上传失败');
  console.error('Upload error:', error);
};

// 输入变化处理
const onInputChange = () => {
  emits('update:modelValue', inputValue.value);
  emits('change', inputValue.value);
};

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (isObjectOrArrayType.value && typeof newValue === 'object') {
      jsonString.value = JSON.stringify(newValue, null, 2);
      inputValue.value = newValue;
    } else {
      inputValue.value = newValue;
    }
    // 标记已初始化
    isInitialized.value = true;
  },
  { immediate: true },
);

// 监听数据类型变化，只在已初始化且值不兼容时才重置值
watch(
  () => props.dataType,
  (newDataType, oldDataType) => {
    // 如果还未初始化，不进行重置
    if (!isInitialized.value) {
      return;
    }

    // 如果数据类型没有实际变化，不进行重置
    if (newDataType === oldDataType) {
      return;
    }

    // 检查当前值是否与新类型兼容
    const currentValue = inputValue.value;
    const isCompatible = isValueCompatibleWithType(currentValue, newDataType);

    // 只有在值不兼容时才重置
    if (!isCompatible) {
      inputValue.value = getDefaultValue();
      jsonString.value = '';
      jsonError.value = '';
      onInputChange();
    }
  },
);

// 获取默认值
const getDefaultValue = () => {
  switch (props.dataType) {
    case 'bool':
      return false;
    case 'int':
    case 'short':
    case 'long':
    case 'decimal':
    case 'double':
    case 'float':
      return 0;
    case 'object':
      return {};
    case 'array':
      return [];
    case 'DateTime':
      return new Date().toISOString();
    default:
      return '';
  }
};

// 检查值是否与数据类型兼容
const isValueCompatibleWithType = (value: any, dataType: string): boolean => {
  if (value === null || value === undefined) {
    return true; // null/undefined 可以兼容任何类型
  }

  switch (dataType) {
    case 'bool':
      return typeof value === 'boolean';
    case 'int':
    case 'short':
    case 'long':
    case 'decimal':
    case 'double':
    case 'float':
      return typeof value === 'number' && !isNaN(value);
    case 'string':
    case 'char':
      return typeof value === 'string';
    case 'object':
      return typeof value === 'object' && !Array.isArray(value);
    case 'array':
      return Array.isArray(value);
    case 'DateTime':
    case 'Date':
      return value instanceof Date || typeof value === 'string';
    case 'byte[]':
      return typeof value === 'string' || value instanceof File;
    default:
      return true; // 未知类型默认兼容
  }
};

// 注意：数组和对象编辑器相关方法已移至 StructuredEditor.vue 组件
</script>

<style lang="less" scoped>
.data-type-input {
  width: 100%;

  .json-editor-container {
    .json-error {
      margin-top: 8px;
    }
  }

  /* 结构化编辑器样式已移至 StructuredEditor.vue 组件 */
}
</style>
