import{d as y,h as a,ab as d,ac as O,ad as m}from"./index-D24a7sNI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1 3V21H23V3H1ZM5 9H9.5V11H5V9ZM5 13H9.5V15H5V13ZM18 10C18 10.9537 17.466 11.7826 16.6807 12.2042C18.0328 12.6886 19 13.9813 19 15.5V16.5H12V15.5C12 13.9813 12.9672 12.6886 14.3193 12.2042C13.534 11.7826 13 10.9537 13 10C13 8.61929 14.1193 7.5 15.5 7.5C16.8807 7.5 18 8.61929 18 10Z"}}]},g=y({name:"VerifyFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-verify-filled",l.value]),f=a(()=>s(s({},c.value),r.style)),u=a(()=>({class:p.value,style:f.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(C,u.value)}});export{g as default};
