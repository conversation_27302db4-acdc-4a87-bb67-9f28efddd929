import{d as f,h as a,ab as v,ac as O,ad as y}from"./index-D24a7sNI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M19.5 23C17.567 23 16 21.433 16 19.5C16 17.9145 17.0543 16.5752 18.5 16.1449V14C18.5 13.4478 18.0523 13 17.5 13H6.5C5.94772 13 5.5 13.4478 5.5 14L5.5 16.1449C6.94574 16.5752 8 17.9145 8 19.5C8 21.433 6.433 23 4.5 23C2.567 23 1 21.433 1 19.5C1 17.9145 2.05425 16.5752 3.5 16.1449V14C3.5 12.3432 4.84315 11 6.5 11H11L11 7.85506C9.55426 7.42479 8.5 6.08551 8.5 4.5C8.5 2.567 10.067 1 12 1C13.933 1 15.5 2.567 15.5 4.5C15.5 6.08551 14.4457 7.42479 13 7.85506L13 11H17.5C19.1569 11 20.5 12.3432 20.5 14V16.1449C21.9457 16.5752 23 17.9145 23 19.5C23 21.433 21.433 23 19.5 23Z"}}]},g=f({name:"TreeRoundDotFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=v(r),p=a(()=>["t-icon","t-icon-tree-round-dot-filled",o.value]),u=a(()=>s(s({},c.value),t.style)),C=a(()=>({class:p.value,style:u.value,onClick:d=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:d})}}));return()=>O(m,C.value)}});export{g as default};
