import{d,h as a,ab as O,ac as y,ad as L}from"./index-D24a7sNI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){L(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3.73497 6.56731L1.83867 6.50327L1.61364 7.23431C0.717737 10.1447 1.42109 13.4458 3.72738 15.7521C6.02641 18.0511 9.31152 18.7556 12.2145 17.8742L17.509 23.1699L19.6318 21.0472L15.3359 16.7514L16.7501 15.3372L21.046 19.633L23.1661 17.5129L17.8702 12.2189C18.7518 9.31581 18.0474 6.03045 15.7482 3.73129C13.4419 1.42499 10.1408 0.721643 7.23041 1.61754L6.49936 1.84258L6.5634 3.73888L10.0913 7.26682L7.26291 10.0952L3.73497 6.56731Z"}}]},g=d({name:"ToolsFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-tools-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{g as default};
