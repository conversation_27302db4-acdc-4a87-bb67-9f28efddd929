import{d as R,h as V,r as l,f as n,b as A,o as B,n as d,g as p,M as i}from"./index-D24a7sNI.js";const N={name:"UploadModal"},x=R({...N,props:{visible:{type:<PERSON><PERSON>an}},emits:["update:visible"],setup(r,{emit:_}){const f=r,m=_,c=V({get(){return f.visible},set(e){m("update:visible",e)}}),v=l(),u=l([]),g=l(!1),b=l(!1),h=l(!0),E=l(!1),S=l(!1),F=e=>{console.log("点击了确认按钮",e),visible.value=!1},L=e=>{console.log("关闭弹窗，点击关闭按钮、按下ESC、点击蒙层等触发",e)},I=({file:e})=>{i.error(`文件 ${e.name} 上传失败`)};function M(e,o){console.log("onSelectChange",e,o)}const U=e=>{console.log("success",e),i.success("上传成功")},O=e=>{console.log("onOneFileSuccess",e)},C=e=>{const{files:o,type:s}=e;console.log("onValidate",s,o);const a={FILE_OVER_SIZE_LIMIT:"文件大小超出限制，已自动过滤",FILES_OVER_LENGTH_LIMIT:"文件数量超出限制，仅上传未超出数量的文件",FILTER_FILE_SAME_NAME:"不允许上传同名文件",BEFORE_ALL_FILES_UPLOAD:"beforeAllFilesUpload 方法拦截了文件",CUSTOM_BEFORE_UPLOAD:"beforeUpload 方法拦截了文件"};a[s]&&i.warning(a[s])};return(e,o)=>{const s=n("t-upload"),a=n("t-space"),w=n("t-dialog");return B(),A(w,{visible:c.value,"onUpdate:visible":o[1]||(o[1]=t=>c.value=t),header:"导入当前环境",width:"40%","confirm-on-enter":!0,"on-close":L,"on-confirm":F},{default:d(()=>[p(a,{direction:"vertical",style:{width:"100%"}},{default:d(()=>[p(s,{ref_key:"uploadRef1",ref:v,modelValue:u.value,"onUpdate:modelValue":o[0]||(o[0]=t=>u.value=t),action:"https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo",headers:{a:"N1",b:"N2"},placeholder:"要求文件大小在 1M 以内",multiple:g.value,"auto-upload":h.value,"upload-all-files-in-one-request":b.value,"is-batch-upload":E.value,"size-limit":{size:1,unit:"MB"},max:5,disabled:S.value,"allow-upload-duplicate-file":!0,onSelectChange:M,onFail:I,onSuccess:U,onOneFileSuccess:O,onValidate:C},null,8,["modelValue","multiple","auto-upload","upload-all-files-in-one-request","is-batch-upload","disabled"])]),_:1})]),_:1},8,["visible"])}}});export{x as _};
