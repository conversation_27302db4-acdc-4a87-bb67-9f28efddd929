<template>
  <t-dialog
    attach="body"
    :header="'详情 > ' + processName + (currentStep?.seqNo > 0 ? ' > ' + currentStep?.stepName : '')"
    width="80%"
    top="72px"
    :footer="false"
  >
    <t-row style="flex-wrap: nowrap">
      <t-col flex="350px">
        <action-form-title title="执行日志"></action-form-title>
        <div class="filter-container">
          <t-row :gutter="[8, 8]">
            <t-col :span="12">
              <t-select
                v-model="functionFilter"
                placeholder="选择函数"
                clearable
                size="small"
                :options="functionOptions"
              />
            </t-col>
            <t-col :span="8">
              <t-input v-model="nameFilter" placeholder="过滤名称" clearable size="small" />
            </t-col>
            <t-col :span="4">
              <t-input-number
                v-model="durationFilter"
                theme="normal"
                placeholder="最小耗时(ms)"
                clearable
                size="small"
                style="width: 100%"
              />
            </t-col>
          </t-row>
        </div>
        <t-list class="action-list" :split="true">
          <t-list-item
            v-for="item in filteredSteps"
            :key="item.id"
            :class="`action-list-item${item.id === currentStep?.id ? ' action-list-item-active' : ''}`"
            @click="onClickStep(item)"
          >
            <div v-if="item.seqNo > 0" class="progress-bar" :style="{ width: getProgressWidth(item) + '%' }"></div>
            <div class="list-item-wrapper">
              <t-space size="small">
                <t-tag v-if="item.seqNo > 0" size="small" theme="primary" variant="outline">{{ item.seqNo }}</t-tag>
                <div style="font-size: 12px; padding-right: 8px">
                  {{ item.stepName }}
                </div>
              </t-space>
            </div>
            <template #action>
              <t-tag size="small" theme="success" variant="light-outline">
                {{ formatDuration(item.duration) }}
                <span v-if="item.seqNo > 0"> ({{ getProgressPercent(item) }}%) </span>
              </t-tag>
            </template>
          </t-list-item>
        </t-list>
      </t-col>
      <t-col flex="auto">
        <t-loading v-if="currentStep" :loading="loading" show-overlay class="log-content">
          <action-form-title title="基本信息"></action-form-title>
          <t-descriptions :column="2" size="small" bordered style="width: 720px" table-layout="auto">
            <t-descriptions-item label="名称">{{ currentStep.stepName }}</t-descriptions-item>
            <t-descriptions-item label="状态">
              <log-status-tag :status="currentStep.status" />
            </t-descriptions-item>
            <t-descriptions-item label="开始时间">
              {{ currentStep.beginTime }}
            </t-descriptions-item>
            <t-descriptions-item label="结束时间">
              {{ currentStep.endTime }}
            </t-descriptions-item>
            <t-descriptions-item label="运行时长">
              {{ formatDuration(currentStep.duration) }}
            </t-descriptions-item>
          </t-descriptions>

          <action-form-title title="数据" style="margin-bottom: 0"></action-form-title>
          <t-tabs v-model="activeTab" :default-value="currentStep?.seqNo === 0 ? 4 : 1" @change="onTabChange">
            <t-tab-panel v-if="inputs" :value="1" label="输入参数">
              <div style="margin-top: 8px">
                <t-radio-group v-model="inputFormat" variant="primary-filled" default-value="plaintext">
                  <t-radio-button value="plaintext">默认</t-radio-button>
                  <t-radio-button value="json">Json</t-radio-button>
                  <t-radio-button value="sql">Sql</t-radio-button>
                </t-radio-group>
              </div>
              <editor :value="inputs" read-only :language="inputFormat" class="log-data" style="height: 410px"></editor>
            </t-tab-panel>
            <t-tab-panel v-if="result" :value="2" label="结果">
              <editor :value="result" read-only language="json" class="log-data" style="height: 450px"></editor>
            </t-tab-panel>
            <t-tab-panel v-if="outputs" :value="3" label="输出参数">
              <div style="margin-top: 8px">
                <t-radio-group v-model="outputFormat" variant="primary-filled" default-value="plaintext">
                  <t-radio-button value="plaintext">默认</t-radio-button>
                  <t-radio-button value="json">Json</t-radio-button>
                  <t-radio-button value="html">Html</t-radio-button>
                  <t-radio-button value="javascript">Javascript</t-radio-button>
                </t-radio-group>
              </div>
              <editor
                :value="outputs"
                read-only
                :language="outputFormat"
                class="log-data"
                style="height: 410px"
              ></editor>
            </t-tab-panel>
            <t-tab-panel v-if="currentStep?.seqNo === 0" :value="4" label="统计">
              <div style="margin-top: 16px">
                <t-space>
                  <t-checkbox v-model="mergeSameSteps">合并名称相同步骤</t-checkbox>
                  <t-checkbox v-model="enableSorting" default-checked>耗时排序</t-checkbox>
                  <t-tooltip v-if="showScrollHint" content="数据较多，可滚动查看全部">
                    <t-button theme="primary" variant="text" size="small" @click="scrollToBottom">
                      <template #icon>↓</template>
                      滚动到底部
                    </t-button>
                  </t-tooltip>
                </t-space>
                <div class="chart-container">
                  <t-loading :loading="chartLoading" overlay>
                    <div ref="chartRef" class="chart-box"></div>
                  </t-loading>
                </div>
              </div>
            </t-tab-panel>
          </t-tabs>
          <div v-if="logData">
            <action-form-title title="日志"> </action-form-title>
            <code-preview :code="logData" lang="log" :is-dark="false" />
          </div>
        </t-loading>
      </t-col>
    </t-row>
    <t-dialog v-model:visible="isShowException" width="70%" header="异常详情" :footer="false">
      <editor
        :value="exceptionContent"
        read-only
        :auto-wrap="false"
        language="plaintext"
        style="height: 350px"
      ></editor>
    </t-dialog>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'SingleLogsDialog',
};
</script>
<script setup lang="ts">
import * as echarts from 'echarts';
import { isEmpty } from 'lodash-es';
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { api, Services } from '@/api/system';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import CodePreview from '@/components/code-preview/index.vue';
import Editor from '@/components/editor/index.vue';
import { formatDuration } from '@/utils';

import LogStatusTag from './LogStatusTag.vue';

const props = defineProps<{
  processId: string;
  processName: string;
  isRun: boolean;
}>();

onMounted(() => {
  fetchData();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chartInstance.value) {
    chartInstance.value.dispose();
    chartInstance.value = null;
  }
  // 清理监听器
  if (stopChartWatch) {
    stopChartWatch();
  }
});

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

const steps = ref<any[]>([]);
const currentStep = ref<any>(null);
const runData = ref<any>(null);
const loading = ref(false);
const activeTab = ref<number>(1);

const fetchData = async () => {
  if (!props.processId) return;
  steps.value = await api.run(props.isRun ? Services.flowRunGetSteps : Services.flowHistoryGetSteps, {
    procId: props.processId,
  });
  setTimeout(() => {
    currentStep.value = null;
    if (steps.value.length > 0) {
      onClickStep(steps.value[0]);
      fetchFunctions();
    }
  }, 500);
};

const nameFilter = ref('');
const durationFilter = ref<number | null>(null);
const functionFilter = ref<string | null>(null);
const functionOptions = ref<{ label: string; value: string }[]>([]);

const fetchFunctions = async () => {
  try {
    const functionIds = steps.value.filter((step) => step.functionId && step.seqNo > 0).map((step) => step.functionId);

    if (functionIds.length > 0) {
      const uniqueFunctionIds = Array.from(new Set(functionIds));
      functionOptions.value = await api.run(Services.functionGetByIds, {
        ids: uniqueFunctionIds,
      });
    }
  } catch (error) {
    console.error('获取函数列表失败', error);
  }
};

const filteredSteps = computed(() => {
  return steps.value.filter((step) => {
    if (nameFilter.value && !step.stepName.toLowerCase().includes(nameFilter.value.toLowerCase())) {
      return false;
    }

    if (durationFilter.value !== null && step.duration < durationFilter.value) {
      return false;
    }

    if (functionFilter.value && step.functionId !== functionFilter.value) {
      return false;
    }

    return true;
  });
});

const getTotalDuration = computed(() => {
  const mainStep = steps.value.find((step) => step.seqNo === 0);
  return mainStep ? mainStep.duration : 0;
});

const getProgressWidth = (item: any) => {
  if (item.seqNo === 0 || getTotalDuration.value === 0) return 0;
  return (item.duration / getTotalDuration.value) * 100;
};

const getProgressPercent = (item: any) => {
  if (item.seqNo === 0 || getTotalDuration.value === 0) return 0;
  return Math.round((item.duration / getTotalDuration.value) * 100);
};

watch(
  () => props.processId,
  () => {
    fetchData();
  },
  { immediate: true },
);

const onClickStep = async (item: any) => {
  loading.value = true;
  currentStep.value = item;
  const dataStr = await api
    .run(props.isRun ? Services.flowRunGetStepData : Services.flowHistoryGetStepData, {
      procId: props.processId,
      stepId: item.id,
    })
    .finally(() => {
      loading.value = false;
    });
  runData.value = JSON.parse(dataStr);

  if (item.seqNo === 0) {
    activeTab.value = 4;
    nextTick(() => {
      setTimeout(() => {
        initChart();
      }, 300);
    });
  } else if (activeTab.value === 4) {
    activeTab.value = 1;
  }
};

const logData = computed(() => {
  return runData.value?.logs?.map((log: any) => `${log.time} [${log.level}] ${log.message}`).join('\n');
});

const chartRef = ref<HTMLElement | null>(null);
const chartInstance = ref<echarts.ECharts | null>(null);
const mergeSameSteps = ref(false);
const enableSorting = ref(true);
const chartLoading = ref(false);

const getChartHeight = (dataCount: number) => {
  // 每条数据需要的高度，基础高度为400px，每增加一条数据增加30px高度，最小高度为400px
  const baseHeight = 400;
  const heightPerItem = 30;
  return Math.max(baseHeight, baseHeight + (dataCount - 10) * heightPerItem); // 如果低于10条使用基础高度
};

const onTabChange = (tabValue: number) => {
  activeTab.value = tabValue;

  // 如果切换到统计Tab并且是主步骤，则初始化图表
  if (tabValue === 4 && currentStep.value?.seqNo === 0) {
    nextTick(() => {
      setTimeout(() => {
        initChart();
      }, 300);
    });
  }
};

// 监听器引用
let stopChartWatch: (() => void) | null = null;

const initChart = () => {
  if (!chartRef.value) {
    console.warn('图表容器不存在，无法初始化图表');
    return;
  }

  try {
    chartLoading.value = true;

    if (chartInstance.value) {
      chartInstance.value.dispose();
      chartInstance.value = null;
    }

    // 确保DOM已更新
    nextTick(() => {
      // 强制等待DOM渲染完成
      setTimeout(() => {
        if (chartRef.value) {
          try {
            chartInstance.value = echarts.init(chartRef.value);

            // 增加图表加载完成的监听
            chartInstance.value.on('finished', () => {
              chartLoading.value = false;
            });

            updateChart();

            // 触发一次resize确保图表正确渲染
            window.dispatchEvent(new Event('resize'));

            // 设置超时保护，避免finished事件不触发的情况
            setTimeout(() => {
              chartLoading.value = false;
              nextTick(() => {
                updateChart();
              });
            }, 500);
          } catch (error) {
            console.error('图表初始化异常:', error);
            chartLoading.value = false;
          }
        } else {
          console.warn('图表容器已不存在');
          chartLoading.value = false;
        }
      }, 200);
    });

    // 使用一次性监听，避免重复添加监听
    stopChartWatch = watch([mergeSameSteps, enableSorting], () => {
      nextTick(() => {
        updateChart();
      });
    });
  } catch (error) {
    chartLoading.value = false;
    console.error('初始化图表失败:', error);
  }
};

const updateChart = () => {
  if (!chartInstance.value) {
    console.warn('图表实例不存在，无法更新图表');
    return;
  }

  try {
    // 获取所有子步骤（seqNo > 0）
    let chartData = steps.value.filter((step) => step.seqNo > 0);

    // 合并同名步骤逻辑
    if (mergeSameSteps.value) {
      const mergedData: Record<string, any> = {};
      chartData.forEach((step) => {
        if (!mergedData[step.stepName]) {
          mergedData[step.stepName] = { ...step, count: 1 };
        } else {
          mergedData[step.stepName].duration += step.duration;
          mergedData[step.stepName].count += 1;
        }
      });
      chartData = Object.values(mergedData);
    }

    // 根据选项排序
    if (enableSorting.value) {
      // 按耗时降序排序
      chartData.sort((a, b) => b.duration - a.duration);
    } else {
      // 按步骤序号升序排序
      chartData.sort((a, b) => a.seqNo - b.seqNo);
    }

    // 为图表准备数据
    // 反转数据，使图表从上到下按顺序显示
    const displayData = [...chartData].reverse();

    // 调整图表容器高度
    if (chartRef.value) {
      const height = getChartHeight(displayData.length);
      chartRef.value.style.height = `${height}px`;
      // 如果数据超过15条，显示滚动提示
      showScrollHint.value = displayData.length > 15;
    }

    // 准备Y轴数据（步骤名称）
    const yAxisData = displayData.map((item) => {
      let name = item.stepName;
      if (mergeSameSteps.value && item.count > 1) {
        name += ` (${item.count}次)`;
      }
      return name;
    });

    // 准备柱状图数据（耗时）
    const seriesData = displayData.map((item) => item.duration);

    chartInstance.value.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter(params: any) {
          // 获取正确的数据项
          const { dataIndex } = params[0];
          const item = displayData[dataIndex];

          let text = `${item.stepName}<br/>耗时: ${formatDuration(item.duration)}`;
          if (mergeSameSteps.value && item.count > 1) {
            text += `<br/>次数: ${item.count}`;
          }
          text += `<br/>占比: ${getProgressPercent(item)}%`;
          text += '<br/><span style="color:#999;font-size:90%">点击可查看详情</span>';
          return text;
        },
      },
      grid: {
        left: '250px',
        right: '70px',
        bottom: '3%',
        top: '40px',
        containLabel: false,
      },
      xAxis: {
        type: 'value',
        name: '耗时(ms)',
        nameLocation: 'end',
        nameTextStyle: {
          padding: [0, 20, 0, 0],
          align: 'right',
        },
        nameGap: 15,
        position: 'top',
        axisLabel: {
          formatter(value: number) {
            // 对较大数值进行更友好的显示
            if (value >= 1000) {
              return `${Math.round(value / 1000)}k`;
            }
            return value;
          },
          margin: 8,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee',
          },
        },
      },
      yAxis: {
        type: 'category',
        data: yAxisData,
        axisLabel: {
          width: 250,
          overflow: 'break',
          lineHeight: 20,
          formatter(value: string) {
            // 如果文本过长，进行换行处理
            if (value.length > 30) {
              return `${value.substring(0, 30)}...`;
            }
            return value;
          },
        },
        axisLine: {
          lineStyle: {
            width: 1,
          },
        },
      },
      series: [
        {
          name: '耗时',
          type: 'bar',
          data: seriesData,
          barWidth: '50%', // 调整柱子宽度为50%
          itemStyle: {
            color(params: any) {
              // 使用正确的数据项计算百分比
              const item = displayData[params.dataIndex];
              const percent = getProgressPercent(item);
              return `rgba(65, 159, 238, ${Math.min(0.3 + (percent / 100) * 0.7, 1)})`;
            },
            borderRadius: [0, 4, 4, 0], // 右侧圆角
          },
          label: {
            show: true,
            position: 'right',
            distance: 10, // 距离柱子10px
            formatter(params: any) {
              return formatDuration(params.value);
            },
          },
          emphasis: {
            itemStyle: {
              color(params: any) {
                const item = displayData[params.dataIndex];
                const percent = getProgressPercent(item);
                return `rgba(65, 159, 238, ${Math.min(0.5 + (percent / 100) * 0.5, 1)})`;
              },
            },
            label: {
              fontSize: 14,
            },
          },
        },
      ],
      legend: {
        data: ['步骤耗时'],
        top: '10px',
        left: '250px',
      },
    });

    // 修改点击事件处理函数，增加高亮效果
    chartInstance.value.off('click');
    chartInstance.value.on('click', (params: any) => {
      if (params.seriesType === 'bar') {
        const { dataIndex } = params;
        const clickedItem = displayData[dataIndex];

        // 定义要跳转到的步骤
        let targetStep = null;

        // 先尝试通过ID精确匹配
        const idMatchedStep = steps.value.find((step) => step.id === clickedItem.id);
        if (idMatchedStep) {
          targetStep = idMatchedStep;
        }
        // 如果是合并的数据，需要找到匹配的步骤
        else if (mergeSameSteps.value) {
          const matchingSteps = steps.value.filter((step) => step.stepName === clickedItem.stepName && step.seqNo > 0);

          if (matchingSteps.length > 0) {
            // 默认选择第一个匹配的步骤
            [targetStep] = matchingSteps;

            // 如果有多个匹配的步骤，提供提示信息
            if (matchingSteps.length > 1) {
              console.log(`找到${matchingSteps.length}个匹配的"${clickedItem.stepName}"步骤`);
            }
          }
        }

        if (targetStep) {
          // 添加过渡效果
          chartLoading.value = true;

          // 切换到相应的步骤
          setTimeout(() => {
            activeTab.value = 1; // 先切换到第一个Tab
            onClickStep(targetStep);
            chartLoading.value = false;

            // 滚动到左侧列表中的对应项并高亮显示
            nextTick(() => {
              const listItem = document.querySelector(`.action-list-item-active`);
              if (listItem) {
                listItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
              }
            });
          }, 200);
        }
      }
    });
  } catch (error) {
    console.error('更新图表失败:', error);
  }
};

const formatJson = (jsonStr: string) => {
  if (isEmpty(jsonStr)) return null;
  if (jsonStr.startsWith('"') && jsonStr.endsWith('"')) {
    jsonStr = JSON.parse(jsonStr);
  }
  try {
    const result = JSON.stringify(JSON.parse(jsonStr), null, 2);
    return result;
  } catch (error) {
    return jsonStr;
  }
};
const inputs = computed(() => {
  const format = inputFormat.value;
  if (format === 'json') {
    return formatJson(runData.value?.inputs);
  }
  return runData.value?.inputs;
});
const result = computed(() => formatJson(runData.value?.result));
const outputs = computed(() => {
  const format = outputFormat.value;
  if (format === 'json') {
    return formatJson(runData.value?.outputs);
  }
  return runData.value?.outputs;
});

const inputFormat = ref('json');
const outputFormat = ref('json');
const isShowException = ref(false);
const exceptionContent = ref('');
const onClickErrorDetail = (log: any) => {
  isShowException.value = true;
  exceptionContent.value = log.exception;
};

// 声明滚动提示变量和滚动函数
const showScrollHint = ref(false);
const scrollToBottom = () => {
  if (chartRef.value) {
    const container = chartRef.value.closest('.chart-container');
    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth',
      });
    }
  }
};
</script>
<style lang="less" scoped>
.filter-container {
  margin-bottom: 12px;
  padding-right: 8px;
}

.action-list {
  width: 100%;
  max-height: 720px;
  overflow-y: auto;
}

.log-content {
  border-left: 1px solid var(--td-border-level-1-color);
  padding: 0 16px;
  min-height: 320px;
}

.log-item {
  border-bottom: 1px solid var(--td-border-level-1-color);
  padding: 3px 8px;
  font-size: 12px;
}

.log-data {
  margin-top: 8px;
}

.action-list-item {
  cursor: default;
  transition: background 0.3s ease;
  position: relative;

  &:hover,
  &.action-list-item-active {
    background-color: var(--td-bg-color-container-hover);
    font-weight: bold;
  }

  &.action-list-item-active {
    color: var(--td-brand-color);
  }
}

.list-item-wrapper {
  position: relative;
  width: 100%;
  z-index: 1;
}

.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(65, 159, 238, 0.1);
  z-index: 1;
}

.chart-container {
  margin-top: 16px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 4px;
  padding: 8px;
  max-height: 600px; // 设置最大高度
  overflow-y: auto; // 允许垂直滚动
}

.chart-box {
  width: 100%;
  height: 500px;
  background-color: #ffffff;

  :deep(.echarts) {
    cursor: pointer;
  }
}
</style>
