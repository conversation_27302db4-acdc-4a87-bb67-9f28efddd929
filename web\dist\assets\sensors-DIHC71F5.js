import{d as v,h as a,ab as f,ac as O,ad as d}from"./index-D24a7sNI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function i(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"g",attrs:{clipPath:"url(#clip0_8726_7450)"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22 11.9993C22 9.56298 21.13 7.33251 19.6828 5.59765L19.0422 4.82977L20.5779 3.54858L21.2185 4.31646C22.9546 6.39749 24 9.07761 24 11.9993C24 14.9209 22.9546 17.601 21.2185 19.6821L20.5779 20.4499L19.0422 19.1688L19.6828 18.4009C21.13 16.666 22 14.4355 22 11.9993ZM17.3773 7.51746C18.3899 8.73125 19 10.2953 19 11.9993C19 13.7032 18.3899 15.2673 17.3773 16.4811L16.7367 17.2489L15.201 15.9677L15.8416 15.1999C16.5654 14.3323 17 13.2178 17 11.9993C17 10.7807 16.5654 9.66626 15.8416 8.79866L15.201 8.03078L16.7367 6.74959L17.3773 7.51746ZM12 10C13.1046 10 14 10.8955 14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12C10 10.8955 10.8954 10 12 10ZM8.79903 8.03078L8.15844 8.79866C7.43464 9.66626 7 10.7807 7 11.9993C7 13.2178 7.43464 14.3323 8.15844 15.1999L8.79903 15.9677L7.26327 17.2489L6.62268 16.4811C5.61009 15.2673 5 13.7032 5 11.9993C5 10.2953 5.61009 8.73125 6.62268 7.51747L7.26327 6.74959L8.79903 8.03078ZM4.95783 4.82977L4.31723 5.59765C2.86995 7.33251 2 9.56298 2 11.9993C2 14.4355 2.86995 16.666 4.31723 18.4009L4.95783 19.1687L3.42207 20.4499L2.78147 19.6821C1.0454 17.601 -1.2771e-07 14.9209 0 11.9993C1.2771e-07 9.07761 1.0454 6.39749 2.78148 4.31646L3.42207 3.54858L4.95783 4.82977Z"}}]}]},m=v({name:"SensorsIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=f(t),p=a(()=>["t-icon","t-icon-sensors",o.value]),C=a(()=>i(i({},c.value),r.style)),u=a(()=>({class:p.value,style:C.value,onClick:L=>{var s;return(s=e.onClick)===null||s===void 0?void 0:s.call(e,{e:L})}}));return()=>O(y,u.value)}});export{m as default};
