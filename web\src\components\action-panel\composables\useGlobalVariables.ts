import { computed } from 'vue';
import { useActionFlowStore } from '../store';

/**
 * 全局变量管理的组合式函数
 * 提供统一的全局变量访问和管理接口
 */
export function useGlobalVariables() {
  const actionFlowStore = useActionFlowStore();

  /**
   * 获取原始全局变量列表
   */
  const globalVariables = computed(() => actionFlowStore.globalVariables);

  /**
   * 获取处理后的全局变量列表（扁平化，包含正确路径）
   */
  const processedGlobalVariables = computed(() => actionFlowStore.processedGlobalVariables);

  /**
   * 全局变量加载状态
   */
  const isLoading = computed(() => actionFlowStore.globalVariablesLoading);

  /**
   * 最后更新时间
   */
  const lastUpdated = computed(() => actionFlowStore.globalVariablesLastUpdated);

  /**
   * 加载全局变量（带缓存）
   * @param force 是否强制刷新
   */
  const loadGlobalVariables = async (force: boolean = false) => {
    return actionFlowStore.loadGlobalVariables(force);
  };

  /**
   * 防抖加载全局变量
   * @param force 是否强制刷新
   */
  const debouncedLoadGlobalVariables = async (force: boolean = false) => {
    return actionFlowStore.debouncedLoadGlobalVariables(force);
  };

  /**
   * 强制刷新全局变量
   */
  const refreshGlobalVariables = async () => {
    return actionFlowStore.refreshGlobalVariables();
  };

  /**
   * 清空全局变量缓存
   */
  const clearCache = () => {
    actionFlowStore.clearGlobalVariablesCache();
  };

  /**
   * 检查缓存是否有效
   * @param maxAge 最大缓存时间（毫秒），默认5分钟
   */
  const isCacheValid = (maxAge: number = 5 * 60 * 1000) => {
    if (!lastUpdated.value) return false;
    return Date.now() - lastUpdated.value < maxAge;
  };

  return {
    // 数据
    globalVariables,
    processedGlobalVariables,
    isLoading,
    lastUpdated,
    
    // 方法
    loadGlobalVariables,
    debouncedLoadGlobalVariables,
    refreshGlobalVariables,
    clearCache,
    isCacheValid,
  };
}
