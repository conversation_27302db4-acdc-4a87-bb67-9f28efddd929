<template>
  <div
    class="simple-value-input"
    :class="{ 'is-disabled': disabled, 'has-value': hasValue }"
    @click="onEdit"
    @contextmenu.prevent="handleContextMenu"
    @mouseover.stop="showEdit = true"
    @mouseleave.stop="showEdit = false"
    @blur="showEdit = false"
  >
    <div class="value-content" :class="{ 'has-error': hasError }">
      <!-- 文本值 -->
      <span v-if="dataValue?.type === 'text'" class="value-text">
        {{ dataValue.textValue || placeholder }}
      </span>

      <!-- 变量值 -->
      <span v-else-if="dataValue?.type === 'variable'" class="value-text variable" :class="{ invalid: hasError }">
        <t-icon name="link" class="value-icon" />
        {{ displayText }}
        <t-icon v-if="hasError" name="error-circle" class="error-icon" />
      </span>

      <!-- 脚本值 -->
      <span v-else-if="dataValue?.type === 'script'" class="value-text script">
        <t-icon name="code" class="value-icon" />
        {{ dataValue.scriptName || '点击编写脚本' }}
      </span>

      <!-- 可视化值 -->
      <span v-else-if="dataValue?.type === 'visual'" class="value-text visual">
        <t-icon name="view-module" class="value-icon" />
        {{ dataValue.visualName || `${dataValue.visualSteps?.length || 0} 个步骤` }}
      </span>

      <!-- 默认状态 -->
      <span v-else class="value-text placeholder">
        {{ placeholder }}
      </span>
    </div>

    <!-- 操作按钮区域 -->
    <div v-if="showEdit && hasValue" class="action-buttons">
      <!-- 清除按钮 -->
      <t-button
        v-if="showReset"
        variant="text"
        size="small"
        @click.stop="onReset"
        :title="'清除'"
        class="action-button"
      >
        <template #icon>
          <t-icon name="close" />
        </template>
      </t-button>
    </div>

    <!-- 类型标识（可选） -->
    <div v-if="showTypeIndicator && typeText" class="type-badge">
      {{ typeText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import ContextMenu from '@imengyu/vue3-context-menu';
import { useClipboard } from '@vueuse/core';
import { cloneDeep } from 'lodash-es';
import { computed, watch, onMounted, onActivated, ref, h, watchEffect } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { FlowData, FlowDataValue } from './model';
import { useVariableReference } from './composables/useVariableReference';
import { useActionFlowStore } from './store';
import { getRandomId } from './utils';
import { debugVariables } from './utils/variableProcessor';
import { CloseIcon, CopyIcon, PasteIcon } from 'tdesign-icons-vue-next';

interface Props {
  dataValue?: FlowDataValue;
  dataType?: string;
  onlyVariable?: boolean;
  limitTypes?: string[];
  placeholder?: string;
  showTypeIndicator?: boolean;
  availableVariables?: FlowData[];
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  showReset?: boolean;
}

interface Emits {
  (e: 'update:data-value', value: FlowDataValue): void;
  (e: 'variable-change', value: FlowDataValue): void;
  (e: 'edit'): void;
  (e: 'reset'): void;
  (e: 'copy', value: FlowDataValue): void;
  (e: 'paste', value: FlowDataValue): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '点击设置值',
  showTypeIndicator: false,
  disabled: false,
  size: 'small',
  showReset: true,
});

const emits = defineEmits<Emits>();

// 组件状态
const showEdit = ref(false);

const actionFlowStore = useActionFlowStore();

// 剪贴板功能
const { isSupported, copy } = useClipboard();

// 创建默认值
const createDefaultValue = (): FlowDataValue => ({
  type: props.onlyVariable ? 'variable' : 'text',
  dataType: props.dataType || props.limitTypes?.join(',') || '',
  textValue: '',
  variableType: '',
  variableName: '',
  variableValue: '',
  scriptName: '',
  scriptValue: '',
  visualSteps: [],
  visualName: '',
});

// 内部数据值管理（类似ValueInput的实现）
const dataValue = ref<FlowDataValue>(props.dataValue || createDefaultValue());

// 监听 props 变化，同步更新数据值（与ValueInput保持完全一致）
watchEffect(() => {
  const newValue = props.dataValue || createDefaultValue();
  console.log('SimpleValueInput watchEffect - props变化:', {
    oldDataValue: dataValue.value,
    newPropsDataValue: props.dataValue,
    newValue,
    isEqual: JSON.stringify(dataValue.value) === JSON.stringify(newValue),
  });

  // 完全同步，不添加任何保护机制（与ValueInput一致）
  dataValue.value = newValue;
});

// 计算属性：是否有值
const hasValue = computed(() => {
  const { type, textValue, variableName, variableValue, scriptName, visualName } = dataValue.value;

  switch (type) {
    case 'text':
      return Boolean(textValue);
    case 'variable':
      return Boolean(variableName || variableValue);
    case 'script':
      return Boolean(scriptName);
    case 'visual':
      return Boolean(visualName);
    default:
      return false;
  }
});

// 初始化全局变量（使用 store 中的防抖加载）
const initializeGlobalVariables = async () => {
  try {
    // 使用 store 中的防抖加载方法
    await actionFlowStore.debouncedLoadGlobalVariables();
  } catch (error) {
    console.error('SimpleValueInput: 获取全局变量失败:', error);
  }
};

// 组件挂载时初始化
onMounted(async () => {
  console.log('SimpleValueInput 组件挂载:', {
    dataValue: dataValue.value,
    variableType: dataValue.value?.variableType,
    variableValue: dataValue.value?.variableValue,
    type: dataValue.value?.type,
  });
  await initializeGlobalVariables();

  // 挂载后强制触发一次变量引用更新
  if (dataValue.value?.type === 'variable') {
    console.log('组件挂载后触发变量引用更新');
    updateVariableReference();
  }
});
onActivated(async () => {
  console.log('SimpleValueInput 组件激活:', {
    dataValue: dataValue.value,
    variableType: dataValue.value?.variableType,
    variableValue: dataValue.value?.variableValue,
    type: dataValue.value?.type,
  });
  await initializeGlobalVariables();

  // 激活后强制触发一次变量引用更新
  if (dataValue.value?.type === 'variable') {
    console.log('组件激活后触发变量引用更新');
    updateVariableReference();
  }
});

// 检测是否需要自动解析变量引用
const shouldAutoResolveVariables = computed(() => {
  const currentDataValue = dataValue.value;

  console.log('shouldAutoResolveVariables 检测:', {
    hasDataValue: !!currentDataValue,
    dataValueType: currentDataValue?.type,
    variableType: currentDataValue?.variableType,
    variableRefId: currentDataValue?.variableRefId,
    variableValue: currentDataValue?.variableValue,
    variableName: currentDataValue?.variableName,
    hasPropsAvailableVariables: !!(props.availableVariables && props.availableVariables.length > 0),
  });

  // 如果已经提供了 availableVariables，则不需要自动解析
  if (props.availableVariables && props.availableVariables.length > 0) {
    console.log('已提供 availableVariables，跳过自动解析');
    return false;
  }

  // 只有变量类型才需要自动解析
  if (currentDataValue?.type !== 'variable') {
    console.log('非变量类型，不需要自动解析');
    return false;
  }

  // 情况1: 有 variableType 的变量
  if (currentDataValue.variableType) {
    // 检查是否需要重新解析：
    // 1. 没有 variableRefId
    // 2. 没有 variableValue
    // 3. 有 variableValue 但需要验证引用是否有效
    const hasVariableRefId = !!currentDataValue.variableRefId;
    const hasVariableValue = !!currentDataValue.variableValue;

    // 如果没有基本的引用信息，肯定需要解析
    if (!hasVariableRefId || !hasVariableValue) {
      const shouldResolve = true;
      console.log('缺少基本引用信息，需要解析:', {
        variableType: currentDataValue.variableType,
        hasVariableRefId,
        hasVariableValue,
        shouldResolve,
      });
      return shouldResolve;
    }

    // 如果有引用信息，检查引用是否有效
    // 通过获取对应类型的变量列表来验证
    const variables = getAvailableVariablesByType(currentDataValue.variableType);
    const isValidReference = variables.some((variable) => {
      const variablePath = variable.path || variable.key;
      return variablePath === currentDataValue.variableValue || variable.key === currentDataValue.variableValue;
    });

    const shouldResolve = !isValidReference;
    console.log('检查引用有效性:', {
      variableType: currentDataValue.variableType,
      variableValue: currentDataValue.variableValue,
      variablesCount: variables.length,
      isValidReference,
      shouldResolve,
    });
    return shouldResolve;
  }

  // 情况2: 没有 variableType 的变量，也需要自动解析以显示所有变量供选择
  console.log('没有 variableType，需要自动解析显示所有变量');
  return true;
});

// 根据 variableType 获取对应的可用变量列表（使用 store 中的处理逻辑）
const getAvailableVariablesByType = (variableType?: string): FlowData[] => {
  const processedVariables = actionFlowStore.getProcessedVariablesByType(variableType);

  // 使用工具函数输出调试信息
  debugVariables(processedVariables, `getAvailableVariablesByType(${variableType || 'all'})`);

  return processedVariables;
};

// 动态计算可用变量列表
const availableVariables = computed(() => {
  const currentDataValue = dataValue.value;

  console.log('availableVariables 计算触发:', {
    hasPropsAvailableVariables: !!(props.availableVariables && props.availableVariables.length > 0),
    shouldAutoResolve: shouldAutoResolveVariables.value,
    dataValueType: currentDataValue?.type,
    variableType: currentDataValue?.variableType,
    variableValue: currentDataValue?.variableValue,
  });

  // 如果已经提供了 availableVariables，直接使用
  if (props.availableVariables && props.availableVariables.length > 0) {
    console.log('使用提供的 availableVariables');
    return props.availableVariables;
  }

  // 如果需要自动解析，根据 variableType 获取对应的变量列表
  if (shouldAutoResolveVariables.value && currentDataValue?.type === 'variable') {
    const variableType = currentDataValue.variableType;

    // 如果有具体的 variableType，获取对应类型的变量
    if (variableType) {
      const variables = getAvailableVariablesByType(variableType);

      // 调试信息
      console.log('SimpleValueInput: 自动解析特定类型变量', {
        variableType,
        variablesCount: variables.length,
        variableValue: currentDataValue.variableValue,
        variableName: currentDataValue.variableName,
        hasVariableRefId: !!currentDataValue.variableRefId,
        variables: variables.map((v) => ({
          key: v.key,
          path: v.path,
          type: v.type,
          description: v.description,
        })),
      });

      return variables;
    } else {
      // 如果没有 variableType，返回所有变量供选择
      const allVariables = getAvailableVariablesByType();

      console.log('SimpleValueInput: 自动解析所有变量', {
        variablesCount: allVariables.length,
        currentCount: actionFlowStore.currentVariables?.length || 0,
        localCount: actionFlowStore.localVariables?.length || 0,
        globalCount: actionFlowStore.globalVariables?.length || 0,
      });

      return allVariables;
    }
  }

  // 默认返回空数组
  console.log('返回空数组，不满足自动解析条件');
  return [];
});

// 使用变量引用管理（支持自动初始化）
const { resolvedVariableInfo, isValidReference, variableType, updateVariableReference } = useVariableReference(
  computed(() => dataValue.value),
  availableVariables,
);

// 计算显示文本
const displayText = computed(() => {
  if (dataValue.value?.type === 'variable') {
    // 优先使用解析后的变量信息
    if (isValidReference.value && resolvedVariableInfo.value) {
      return resolvedVariableInfo.value.name || resolvedVariableInfo.value.path;
    }
    // 降级到原有的显示方式
    return dataValue.value.variableName || dataValue.value.variableValue || '点击选择变量';
  }
  return '';
});

// 检查是否有错误
const hasError = computed(() => {
  return dataValue.value?.type === 'variable' && !isValidReference.value && !!dataValue.value.variableValue;
});

// 获取类型文本
const typeText = computed(() => {
  const type = variableType.value || dataValue.value?.dataType || props.dataType;
  if (!type) return '';

  const typeMap: Record<string, string> = {
    string: '文本',
    number: '数字',
    int: '整数',
    decimal: '小数',
    boolean: '布尔',
    object: '对象',
    array: '数组',
    DateTime: '日期',
  };
  return typeMap[type] || type;
});

// 重置数据值
const onReset = () => {
  if (props.disabled) return;

  const defaultValue = createDefaultValue();
  emits('update:data-value', defaultValue);
  emits('variable-change', defaultValue);
  emits('reset');
};

// 复制数据值
const onCopy = async () => {
  if (!hasValue.value) return;

  try {
    const copyData = cloneDeep(dataValue.value);
    const copyText = JSON.stringify(copyData);

    if (isSupported.value) {
      await copy(copyText);
    } else {
      // 降级到 sessionStorage
      sessionStorage.setItem('valueInputCopyText', copyText);
    }

    emits('copy', copyData);
    MessagePlugin.success('复制成功');
  } catch (error) {
    console.error('复制失败:', error);
    MessagePlugin.error('复制失败');
  }
};

// 粘贴数据值
const onPaste = async () => {
  if (props.disabled) return;

  try {
    const sourceText = isSupported.value
      ? await navigator.clipboard.readText()
      : sessionStorage.getItem('valueInputCopyText');

    if (!sourceText) {
      MessagePlugin.warning('剪贴板为空');
      return;
    }

    let pasteData: any;
    try {
      pasteData = JSON.parse(sourceText);
    } catch {
      // 尝试作为纯文本粘贴
      const textValue: FlowDataValue = {
        ...createDefaultValue(),
        type: 'text',
        textValue: sourceText,
      };

      emits('update:data-value', textValue);
      emits('variable-change', textValue);
      emits('paste', textValue);

      MessagePlugin.success('已粘贴为文本');
      return;
    }

    // 验证粘贴数据格式
    if (pasteData && typeof pasteData === 'object' && pasteData.type) {
      const newValue: FlowDataValue = {
        ...createDefaultValue(),
        ...pasteData,
        dataType: props.dataType || props.limitTypes?.join(',') || pasteData.dataType,
      };

      emits('update:data-value', newValue);
      emits('variable-change', newValue);
      emits('paste', newValue);

      MessagePlugin.success('粘贴成功');
    } else {
      MessagePlugin.warning('剪贴板内容格式不正确');
    }
  } catch (error) {
    console.error('粘贴失败:', error);
    MessagePlugin.error('粘贴失败，请检查剪贴板权限');
  }
};

// 处理右键菜单
const handleContextMenu = (e: MouseEvent) => {
  if (props.disabled) return;

  const menuItems = [];

  // 复制选项
  if (hasValue.value) {
    menuItems.push({
      label: '复制',
      icon: h(CopyIcon),
      onClick: () => {
        onCopy();
      },
    });
  }

  // 粘贴选项
  menuItems.push({
    label: '粘贴',
    icon: h(PasteIcon),
    onClick: () => {
      onPaste();
    },
  });

  // 清除选项
  if (props.showReset && hasValue.value) {
    menuItems.push({
      label: '清除',
      icon: h(CloseIcon),
      onClick: () => {
        onReset();
      },
    });
  }

  if (menuItems.length > 0) {
    ContextMenu.showContextMenu({
      theme: 'mac',
      x: e.x,
      y: e.y,
      items: menuItems,
    });
  }
};

// 编辑按钮点击
const valueId = ref('');

const onEdit = () => {
  if (props.disabled) return;

  console.log('SimpleValueInput onEdit - 开始编辑:', {
    currentDataValue: dataValue.value,
    propsDataValue: props.dataValue,
    valueId: valueId.value,
  });

  actionFlowStore.isSaveValue = false;
  actionFlowStore.showValueDialog = true;
  valueId.value = getRandomId();

  const valueToEdit = cloneDeep(dataValue.value);
  console.log('SimpleValueInput onEdit - 传递给Dialog的值:', {
    valueId: valueId.value,
    valueToEdit,
  });

  actionFlowStore.currentValueInputData = {
    id: valueId.value,
    value: valueToEdit,
    onlyVariable: props.onlyVariable,
    limitTypes: props.limitTypes,
  };
  emits('edit');
};

// 监听 store 中的保存事件（与ValueInput保持一致）
watch(
  () => actionFlowStore.isSaveValue,
  (newVal, oldVal) => {
    console.log('SimpleValueInput watch isSaveValue:', {
      newVal,
      oldVal,
      currentValueInputDataId: actionFlowStore.currentValueInputData?.id,
      myValueId: valueId.value,
      isMatch: actionFlowStore.currentValueInputData?.id === valueId.value,
      currentValueInputData: actionFlowStore.currentValueInputData,
    });

    if (newVal !== oldVal && newVal && actionFlowStore.currentValueInputData?.id === valueId.value) {
      const newValue = actionFlowStore.currentValueInputData.value;
      console.log('SimpleValueInput - 保存成功，发送更新事件:', {
        newValue,
        oldDataValue: dataValue.value,
      });

      // 只发送更新事件给父组件，让父组件更新props，然后watchEffect会自动同步内部状态
      emits('update:data-value', newValue);
      emits('variable-change', newValue);
      // 不在这里重置 isSaveValue，保持与ValueInput一致
    }
  },
);

// 监听变量引用变化
watch(
  () => resolvedVariableInfo.value,
  (newInfo) => {
    console.log('resolvedVariableInfo 变化:', {
      newInfo,
      dataValueType: dataValue.value?.type,
      dataValue: dataValue.value,
    });

    if (newInfo && dataValue.value?.type === 'variable') {
      // 当变量信息更新时，触发变更事件
      emits('variable-change', dataValue.value);
    }
  },
  { deep: true },
);
</script>

<style lang="less" scoped>
.simple-value-input {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  border: 1px solid transparent;
  border-radius: 3px;
  background: var(--td-bg-color-container);
  min-height: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: var(--td-border-level-2-color);
    background: var(--td-bg-color-container-hover);
  }

  &:active {
    border-color: var(--td-brand-color);
  }

  // 禁用状态
  &.is-disabled {
    cursor: not-allowed;
    background-color: var(--td-bg-color-component-disabled);
    border-color: var(--td-border-level-1-color);

    .value-text {
      color: var(--td-text-color-disabled);
    }

    &:hover {
      border-color: var(--td-border-level-1-color);
      background-color: var(--td-bg-color-component-disabled);
    }
  }

  // 有值状态
  &.has-value {
    border-color: var(--td-border-level-1-color);
  }

  // 操作按钮区域
  .action-buttons {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }

  .action-button {
    padding: 0;
    min-width: auto;
    width: 16px;
    height: 16px;

    :deep(.t-button__icon) {
      font-size: 12px;
    }

    &:hover {
      background-color: var(--td-bg-color-container-hover);
    }
  }

  .value-content {
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 20px;

    &.has-error {
      .value-text.invalid {
        color: var(--td-error-color);
      }
    }

    .value-text {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: var(--td-text-color-primary);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      &.placeholder {
        color: var(--td-text-color-placeholder);
      }

      &.variable {
        color: var(--td-brand-color);

        &.invalid {
          color: var(--td-error-color);
          text-decoration: line-through;
        }
      }

      &.script {
        color: var(--td-warning-color);
      }

      &.visual {
        color: var(--td-success-color);
      }

      .value-icon {
        font-size: 12px;
        flex-shrink: 0;
      }

      .error-icon {
        color: var(--td-error-color);
        font-size: 12px;
        flex-shrink: 0;
      }
    }
  }

  .type-badge {
    flex-shrink: 0;
    padding: 2px 6px;
    background: var(--td-bg-color-tag);
    color: var(--td-text-color-secondary);
    font-size: 12px;
    border-radius: 3px;
    line-height: 1;
  }
}
</style>
