import{d as v,h as a,ab as d,ac as O,ad as y}from"./index-D24a7sNI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10 0.999512H9V2.99951H10C10.4616 2.99951 10.8472 3.33764 11.1573 4.14394C11.1636 4.1602 11.1697 4.17648 11.1758 4.19278C9.08302 3.85997 6.52982 4.25913 4.44717 6.14817C2.69192 7.74023 1.46806 10.7765 2.2316 14.0207C3.34908 18.7688 7.88655 21.4611 12.1044 23.1245C15.8483 20.5776 18.2754 16.6846 17.8869 12.0399C17.6588 9.31161 16.3351 6.74827 14.0415 5.2681C16.9314 6.85214 18.7279 9.8662 18.9999 13.119C19.2056 15.5793 18.6451 17.8122 17.5628 19.7818C19.0655 18.6923 20.647 17.1965 21.3602 15.3477C22.9941 11.1121 21.3332 7.01451 18.3703 5.32264C16.647 4.33861 15.0391 4.06143 13.8038 4.23493C13.8146 4.20492 13.8258 4.17493 13.8373 4.14503C14.1482 3.33955 14.535 2.99951 15 2.99951H16V0.999512H15C13.7858 0.999512 12.9974 1.66797 12.4982 2.40391C12.0001 1.66813 11.213 0.999512 10 0.999512Z"}}]},b=v({name:"PeachFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=d(t),p=a(()=>["t-icon","t-icon-peach-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(h,C.value)}});export{b as default};
