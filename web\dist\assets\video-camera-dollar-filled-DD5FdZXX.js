import{d as f,h as n,ab as O,ac as m,ad as y}from"./index-D24a7sNI.js";function i(e,a){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var a=1;a<arguments.length;a++){var r=arguments[a]!=null?arguments[a]:{};a%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17 4H0V20H17V15.7232L24 19.7232V4.23382L17 8.43382V4ZM9.5 8H11.5V10H7.5V11L9.5 11C10.6046 11 11.5 11.8954 11.5 13V14C11.5 15.1046 10.6046 16 9.5 16L9.5 17L7.5 17L7.5 16L5.5 16V14H9.5V13H7.5C6.39543 13 5.5 12.1046 5.5 11V10C5.5 8.89543 6.39543 8 7.5 8V7H9.5V8Z"}}]},b=f({name:"VideoCameraDollarFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,a){var{attrs:r}=a,t=n(()=>e.size),{className:l,style:s}=O(t),p=n(()=>["t-icon","t-icon-video-camera-dollar-filled",l.value]),u=n(()=>c(c({},s.value),r.style)),v=n(()=>({class:p.value,style:u.value,onClick:d=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:d})}}));return()=>m(C,v.value)}});export{b as default};
