import{d as f,h as a,ab as L,ac as O,ad as y}from"./index-D24a7sNI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM23 12C23 18.0751 18.0751 23 12 23C5.92487 23 0.999999 18.0751 1 12C1 5.92487 5.92487 0.999999 12 1C18.0751 1 23 5.92487 23 12ZM15.6906 19.2252L11.4831 15.0176C9.81625 15.4628 7.96248 15.0326 6.65294 13.723C5.15953 12.2296 4.80973 10.0284 5.59766 8.20379L5.91624 7.46604L7.27084 7.72756L9.11387 9.57058L9.57251 9.11194L7.72984 7.26927L7.46709 5.91504L8.20579 5.59624C10.0302 4.80888 12.2309 5.15883 13.724 6.65197C15.0335 7.96151 15.4637 9.81528 15.0186 11.4821L19.2262 15.6897L15.6906 19.2252ZM16.3977 15.6897L12.6882 11.9802L12.9512 11.3595C13.4147 10.2656 13.1989 8.95533 12.3098 8.06618C11.8016 7.55801 11.1559 7.26981 10.4916 7.20264L12.4009 9.11194L9.11387 12.399L7.2034 10.4885C7.27017 11.1535 7.55845 11.8001 8.06715 12.3088C8.9563 13.198 10.2666 13.4137 11.3605 12.9502L11.9811 12.6873L15.6906 16.3968L16.3977 15.6897Z"}}]},b=f({name:"ToolsCircleIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=L(t),p=a(()=>["t-icon","t-icon-tools-circle",o.value]),u=a(()=>c(c({},s.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>O(d,C.value)}});export{b as default};
