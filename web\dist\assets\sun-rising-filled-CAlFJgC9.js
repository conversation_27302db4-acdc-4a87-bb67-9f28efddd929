import{d,h as a,ab as O,ac as y,ad as g}from"./index-D24a7sNI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 3V6H11V3H13ZM20.4853 6.92848L18.364 9.0498L16.9497 7.63559L19.0711 5.51426L20.4853 6.92848ZM4.9289 5.51431L7.05022 7.63563L5.63601 9.04984L3.51469 6.92852L4.9289 5.51431ZM6 15V14C6 10.6863 8.68629 8 12 8C15.3137 8 18 10.6863 18 14V15C14.2177 15 12.5765 15 6 15ZM1 13H4V15H1V13ZM20 13H23V15H20V13ZM12 15.7981L15.3028 18H23V20H14.6972L12 18.2018L9.30278 20H1V18H8.69722L12 15.7981Z"}}]},L=d({name:"SunRisingFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=O(t),p=a(()=>["t-icon","t-icon-sun-rising-filled",i.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(m,f.value)}});export{L as default};
