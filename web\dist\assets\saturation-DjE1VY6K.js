import{d as C,h as a,ab as O,ac as y,ad as d}from"./index-D24a7sNI.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 0.0690918L19.0677 7.07007C22.9774 10.9429 22.9774 17.2259 19.0677 21.0987C15.1632 24.9663 8.83675 24.9663 4.93229 21.0987C1.02257 17.2259 1.02257 10.9429 4.93229 7.07007L12 0.0690918ZM12 2.8842L6.33978 8.49098C3.22007 11.5812 3.22007 16.5876 6.33978 19.6778C9.46476 22.7733 14.5352 22.7733 17.6602 19.6778C20.7799 16.5876 20.7799 11.5812 17.6602 8.49098L12 2.8842ZM11 9.03493H12C15.0274 9.03493 17.4816 11.4891 17.4816 14.5165C17.4816 17.5439 15.0274 19.9981 12 19.9981H11L11 9.03493ZM13 11.1807L13 17.8524C14.4355 17.4227 15.4816 16.0918 15.4816 14.5165C15.4816 12.9413 14.4355 11.6104 13 11.1807Z"}}]},g=C({name:"SaturationIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=O(r),p=a(()=>["t-icon","t-icon-saturation",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
