import{d,h as a,ab as O,ac as y,ad as m}from"./index-D24a7sNI.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 20.5001H22V22.5001H2V20.5001zM7 7C7 4.23858 9.23858 2 12 2 14.7614 2 17 4.23858 17 7 17 8.56955 16.2753 10.2975 15.1526 11.4883 14.9901 11.6607 14.8522 11.8446 14.7457 12.0415 14.483 12.5272 14.9477 13 15.5 13L20 13C21.1046 13 22 13.8954 22 15V19.5L2 19.5V15C2 13.8954 2.89543 13 4 13L8.5 13C9.05228 13 9.51703 12.5272 9.25426 12.0415 9.14775 11.8446 9.00993 11.6607 8.84741 11.4883 7.72475 10.2975 7 8.56955 7 7z",fillOpacity:.9}}]},C=d({name:"SealFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-seal-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(b,f.value)}});export{C as default};
