import{d,h as a,ab as O,ac as y,ad as m}from"./index-D24a7sNI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M0.185547 18.9616L8.15951 21.6452L12.8168 16.9879L14.0022 18.1733L22.0591 10.1142C23.8575 8.3153 23.8573 5.3992 22.0587 3.60057C20.2597 1.8016 17.3429 1.80179 15.5442 3.601L0.185547 18.9616ZM11.4026 15.5737L7.62215 19.3541L3.88063 18.0949L8.90225 13.0733L11.4026 15.5737Z"}}]},P=d({name:"SliceFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=O(t),p=a(()=>["t-icon","t-icon-slice-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(b,f.value)}});export{P as default};
