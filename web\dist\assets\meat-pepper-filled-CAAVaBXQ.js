import{d as v,h as a,ab as d,ac as O,ad as y}from"./index-D24a7sNI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function p(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M15.0757 10.7062C15.4262 9.59704 15.6595 8.5216 15.7432 7.52501C15.8402 6.36801 15.7418 5.24609 15.3256 4.28816C14.8962 3.29966 14.1452 2.53048 13.0568 2.11521C11.3621 1.46853 9.72353 1.84042 8.31466 2.7077C6.92936 3.56048 5.70918 4.91873 4.70763 6.40285C4.30184 7.00415 3.92531 7.63597 3.58257 8.2788C3.07349 9.23352 2.63894 10.2126 2.29372 11.1521C1.72761 12.6928 1.37277 14.2056 1.36684 15.3702C1.29642 16.665 1.62209 17.7344 2.30071 18.5192C2.8009 19.0977 3.43919 19.4594 4.10948 19.6573C4.30732 20.3276 4.66897 20.9659 5.24736 21.4661C6.03211 22.1448 7.10153 22.4706 8.39634 22.4002C9.55778 22.3942 11.1185 22.0393 12.7199 21.4775C14.3547 20.904 16.1188 20.0842 17.6779 19.0919C19.2224 18.1089 20.6415 16.9087 21.4982 15.5456C22.3797 14.1432 22.6955 12.4973 21.8373 10.84C21.1919 9.59345 20.2064 8.64174 19.1704 8.00189C18.7243 7.72634 18.2559 7.50083 17.7863 7.33402C18.394 6.77012 19.2434 6.21998 20.2422 5.97021L21.2124 5.7276L20.7271 3.78735L19.757 4.02996C18.5294 4.33696 17.4634 4.96563 16.6417 5.67499C16.6585 5.86134 16.6658 6.04893 16.6641 6.23661C16.6498 7.79232 16.0329 9.42863 15.0757 10.7062Z"}}]},b=v({name:"MeatPepperFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=d(t),c=a(()=>["t-icon","t-icon-meat-pepper-filled",l.value]),C=a(()=>p(p({},s.value),r.style)),u=a(()=>({class:c.value,style:C.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(m,u.value)}});export{b as default};
