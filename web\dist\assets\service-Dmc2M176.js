import{d as C,h as a,ab as O,ac as y,ad as d}from"./index-D24a7sNI.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 11C2 5.47715 6.47715 1 12 1C17.5228 1 22 5.47715 22 11V16.1538C22 17.7996 20.58 19 19 19H16V11H20C20 6.58172 16.4183 3 12 3C7.58172 3 4 6.58172 4 11H8V19H6.06301C6.28503 19.8626 7.06808 20.5 8 20.5H9.56367C9.87991 20.0466 10.4053 19.75 11 19.75H13C13.9665 19.75 14.75 20.5335 14.75 21.5C14.75 22.4665 13.9665 23.25 13 23.25H11C10.4053 23.25 9.87991 22.9534 9.56367 22.5H8C5.90559 22.5 4.18714 20.8903 4.0143 18.8406C2.87438 18.4625 2 17.4453 2 16.1538V11ZM6 17V13H4V16.1538C4 16.5473 4.37084 17 5 17H6ZM20 13H18V17H19C19.6292 17 20 16.5473 20 16.1538V13Z"}}]},g=C({name:"ServiceIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=O(t),p=a(()=>["t-icon","t-icon-service",o.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
