import{d as L,h as a,ab as d,ac as O,ad as y}from"./index-D24a7sNI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M15.6162 3.26835L11.9994 0.186157L8.38263 3.26835L3.64575 3.64636L3.26774 8.38324L0.185547 12L3.26774 15.6168L3.64575 20.3537L8.38264 20.7317L11.9994 23.8139L15.6162 20.7317L20.3531 20.3537L20.7311 15.6168L23.8133 12L20.7311 8.38325L20.3531 3.64636L15.6162 3.26835ZM10.9994 16.4142L6.58521 12L7.99942 10.5858L10.9994 13.5858L16.4994 8.08582L17.9136 9.50003L10.9994 16.4142Z"}}]},g=L({name:"VerifiedFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=d(t),p=a(()=>["t-icon","t-icon-verified-filled",i.value]),f=a(()=>s(s({},c.value),r.style)),u=a(()=>({class:p.value,style:f.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>O(m,u.value)}});export{g as default};
