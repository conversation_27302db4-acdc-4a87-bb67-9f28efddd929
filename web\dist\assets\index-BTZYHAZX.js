import{R as a}from"./index-BiJcr73C.js";import{d as n,f as r,b as p,o as u,n as o,g as l,p as i,t as _}from"./index-D24a7sNI.js";const d={name:"Result404"},b=n({...d,setup(f){return(t,e)=>{const s=r("t-button");return u(),p(a,{title:"404 Not Found",tip:t.t("pages.result.404.subtitle"),type:"404"},{default:o(()=>[l(s,{onClick:e[0]||(e[0]=()=>t.$router.push("/"))},{default:o(()=>[i(_(t.t("pages.result.404.back")),1)]),_:1})]),_:1},8,["tip"])}}});export{b as default};
