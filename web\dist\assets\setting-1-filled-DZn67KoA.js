import{d as C,h as a,ab as d,ac as O,ad as y}from"./index-D24a7sNI.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M14.82 1H9.18034L8.53289 4.23728C7.99386 4.47836 7.48479 4.77378 7.0126 5.11643L3.88377 4.05799L1.06396 8.94203L3.54498 11.1224C3.51532 11.4112 3.50015 11.704 3.50015 12C3.50015 12.2961 3.51532 12.5888 3.54498 12.8776L1.06396 15.058L3.88377 19.942L7.01262 18.8836C7.4848 19.2262 7.99387 19.5216 8.53289 19.7627L9.18034 23H14.82L15.4674 19.7627C16.0064 19.5216 16.5155 19.2262 16.9877 18.8836L20.1165 19.942L22.9363 15.058L20.4553 12.8776C20.485 12.5888 20.5001 12.2961 20.5001 12C20.5001 11.704 20.485 11.4112 20.4553 11.1224L22.9363 8.94203L20.1165 4.05799L16.9877 5.11643C16.5155 4.77378 16.0064 4.47836 15.4674 4.23728L14.82 1ZM12 16C9.79086 16 8 14.2091 8 12C8 9.79086 9.79086 8 12 8C14.2091 8 16 9.79086 16 12C16 14.2091 14.2091 16 12 16Z"}}]},m=C({name:"Setting1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-setting-1-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(L,f.value)}});export{m as default};
