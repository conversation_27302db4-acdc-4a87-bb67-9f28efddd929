import{d as O,h as a,ab as y,ac as C,ad as b}from"./index-D24a7sNI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 2C8.46243 2 6 4.46243 6 7.5 6 10.5376 8.46243 13 11.5 13 14.5376 13 17 10.5376 17 7.5 17 4.46243 14.5376 2 11.5 2zM8 7.5C8 5.567 9.567 4 11.5 4 13.433 4 15 5.567 15 7.5 15 9.433 13.433 11 11.5 11 9.567 11 8 9.433 8 7.5zM4 20C4 17.7909 5.79086 16 8 16H11V14H8C4.68629 14 2 16.6863 2 20V22H11.05V20H4zM23.5898 18C23.5898 18 21.9181 22.5 17.4998 22.5 16.528 22.5 15.6891 22.2823 14.9716 21.9427L14.0001 22.9142 12.5859 21.4999 13.3034 20.7824C11.9712 19.5115 11.4098 18 11.4098 18 11.4098 18 13.0834 13.5 17.4998 13.5 18.4716 13.5 19.3106 13.7179 20.0283 14.0578L21.0002 13.0859 22.4144 14.5002 21.6961 15.2184C23.0279 16.4892 23.5898 18 23.5898 18zM20.2866 16.6279L16.5335 20.3809C16.8315 20.4565 17.1529 20.5 17.498 20.5 20.1136 20.5 21.3685 18 21.3685 18 21.3685 18 21.014 17.2908 20.2866 16.6279zM18.4664 15.6197C18.1674 15.5437 17.8447 15.5 17.498 15.5 14.8774 15.5 13.6285 18 13.6285 18 13.6285 18 13.9845 18.7098 14.7129 19.373L18.4664 15.6197z"}}]},g=O({name:"UserInvisibleIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=y(t),p=a(()=>["t-icon","t-icon-user-invisible",i.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>C(d,v.value)}});export{g as default};
