import{d as v,h as a,ab as d,ac as O,ad as y}from"./index-D24a7sNI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16 1H8V5H16V1Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M3 3H6V7H18V3H21V10.9172C20.3019 10.6478 19.5432 10.5 18.75 10.5C15.2982 10.5 12.5 13.2982 12.5 16.75C12.5 19.313 13.9446 21.3386 15.0255 22.5271C15.1774 22.6941 15.3288 22.8519 15.4771 23H3V3Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M18.75 23.7013C18.6826 23.6564 18.6149 23.6118 18.5471 23.5672C18.363 23.446 18.1786 23.3246 18.0005 23.1949C17.8856 23.1113 17.7252 22.9906 17.5345 22.836C17.1545 22.5281 16.6463 22.0799 16.1352 21.5179C15.1403 20.4239 14 18.7512 14 16.75C14 14.1266 16.1266 12 18.75 12C21.3734 12 23.5 14.1266 23.5 16.75C23.5 18.7512 22.3597 20.4239 21.3648 21.5179C20.8537 22.0799 20.3455 22.5281 19.9655 22.836C19.7748 22.9906 19.6144 23.1113 19.4995 23.1949C19.3213 23.3246 19.137 23.4459 18.9528 23.5672C18.8851 23.6118 18.8174 23.6564 18.75 23.7013ZM17.5 16V18H20V16H17.5Z"}}]},b=v({name:"TaskLocationFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-task-location-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),C=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(g,C.value)}});export{b as default};
