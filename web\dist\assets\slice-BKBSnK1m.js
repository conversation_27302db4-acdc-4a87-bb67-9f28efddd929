import{d as O,h as a,ab as y,ac as d,ad as m}from"./index-D24a7sNI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M0.185547 18.9616L8.15951 21.6452L12.8168 16.9879L14.0022 18.1733L22.0591 10.1142C23.8575 8.3153 23.8573 5.3992 22.0587 3.60057C20.2597 1.8016 17.3429 1.80179 15.5442 3.601L0.185547 18.9616ZM10.3164 11.659L16.9586 5.01502C17.9763 3.99704 19.6266 3.99693 20.6444 5.01478C21.6621 6.03244 21.6622 7.68235 20.6447 8.70014L14.002 15.3446L10.3164 11.659ZM11.4026 15.5737L7.62215 19.3541L3.88063 18.0949L8.90225 13.0733L11.4026 15.5737Z"}}]},L=O({name:"SliceIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=y(t),p=a(()=>["t-icon","t-icon-slice",l.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>d(b,v.value)}});export{L as default};
