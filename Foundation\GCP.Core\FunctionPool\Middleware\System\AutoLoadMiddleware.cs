﻿using GCP.Common;
using Microsoft.Extensions.DependencyInjection;
using Serilog;

namespace GCP.FunctionPool
{
    internal class AutoLoadMiddleware : IFunctionMiddleware
    {
        public static async Task Handler(FunctionContext ctx, Func<Task> next)
        {
            await next();
            if(ctx.Current.Path == "/gcp/functionCode/publishVersion")
            {
                var funcId = ctx.Current.Target?.ToString();
                if (!string.IsNullOrEmpty(funcId))
                {
                    Log.Information("AutoLoadFunction: {funcId}", funcId);
                    FunctionRunner.LoadDbFunction(funcId);
                    FunctionHttpHandler.LoadDbApi(funcId);
                }
            }
            else if (ctx.Current.Path == "/gcp/api/update")
            {
                var funcId = ctx.Current.Target?.ToString();
                if (!string.IsNullOrEmpty(funcId))
                {
                    Log.Information("AutoLoadApi: {funcId}", funcId);
                    FunctionHttpHandler.LoadDbApi(funcId);
                }
            }
        }
    }
}
