import{d,h as a,ab as O,ac as y,ad as b}from"./index-D24a7sNI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),r.push.apply(r,t)}return r}function o(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 2C8.96243 2 6.5 4.46243 6.5 7.5 6.5 10.5376 8.96243 13 12 13 15.0376 13 17.5 10.5376 17.5 7.5 17.5 4.46243 15.0376 2 12 2zM8 14C5.23858 14 3 16.2386 3 19V22H21V19C21 16.2386 18.7614 14 16 14H14.618L12 19.2361 9.38197 14H8z"}}]},P=d({name:"UserBusinessFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:s,style:c}=O(t),u=a(()=>["t-icon","t-icon-user-business-filled",s.value]),p=a(()=>o(o({},c.value),r.style)),f=a(()=>({class:u.value,style:p.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(m,f.value)}});export{P as default};
