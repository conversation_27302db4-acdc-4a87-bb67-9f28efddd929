import{d as v,h as a,ab as O,ac as y,ad as h}from"./index-D24a7sNI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){h(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M20 1H4V23H11.5V17H6V3H18V11.0205C18.7087 11.079 19.3826 11.2607 20 11.544V1Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M20.7495 15.75V16.5H21.998V23H12.998V16.5H14.2495V15.75C14.2495 13.9551 15.7046 12.5 17.4995 12.5C19.2944 12.5 20.7495 13.9551 20.7495 15.75ZM18.7495 15.75C18.7495 15.0596 18.1899 14.5 17.4995 14.5C16.8092 14.5 16.2495 15.0596 16.2495 15.75V16.5H18.7495V15.75Z"}}]},C=v({name:"PhoneLockedFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:s}=O(r),p=a(()=>["t-icon","t-icon-phone-locked-filled",o.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:d=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:d})}}));return()=>y(g,f.value)}});export{C as default};
