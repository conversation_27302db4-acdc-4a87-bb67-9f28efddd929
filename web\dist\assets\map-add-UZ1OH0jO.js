import{d as f,h as a,ab as O,ac as y,ad as m}from"./index-D24a7sNI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7.88647 1.85889L15.9717 5.39618L22 2.38204V11.0001H20V5.61811L17 7.11811V11.0001H15V7.15409L9 4.52909V15.382L11.3396 16.5518L10.4452 18.3407L8.04378 17.14L2 20.7663V5.97942L7.88647 1.85889ZM7 15.4339V4.92073L4 7.02073V17.2339L7 15.4339ZM19 12.0001V16.0001H23V18.0001H19V22.0001H17V18.0001H13V16.0001H17V12.0001H19Z"}}]},g=f({name:"MapAddIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-map-add",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:d=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:d})}}));return()=>y(V,v.value)}});export{g as default};
