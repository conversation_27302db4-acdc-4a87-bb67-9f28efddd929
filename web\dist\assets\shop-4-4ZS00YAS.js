import{d as V,h as a,ab as H,ac as O,ad as y}from"./index-D24a7sNI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7 4C5.89543 4 5 4.89543 5 6V7H8V4H7ZM10 4V7H14V4H10ZM16 4V7H19V6C19 4.89543 18.1046 4 17 4H16ZM21 7H22V9H21V20H22V22H2V20H3V9H2V7H3V6C3 3.79086 4.79086 2 7 2H17C19.2091 2 21 3.79086 21 6V7ZM19 9H5V20H8V15C8 12.7909 9.79086 11 12 11C14.2091 11 16 12.7909 16 15V20H19V9ZM14 20V15C14 13.8954 13.1046 13 12 13C10.8954 13 10 13.8954 10 15V20H14Z"}}]},h=V({name:"Shop4Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=H(t),p=a(()=>["t-icon","t-icon-shop-4",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(d,v.value)}});export{h as default};
