import{d as v,h as a,ab as O,ac as y,ad as d}from"./index-D24a7sNI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6.5 7C6.5 3.96243 8.96243 1.5 12 1.5C15.0376 1.5 17.5 3.96243 17.5 7C17.5 8.70712 16.7194 10.5553 15.5164 11.8313C15.2992 12.0616 15.1538 12.2884 15.0741 12.5H20C21.1046 12.5 22 13.3954 22 14.5V19.5H2V14.5C2 13.3954 2.89543 12.5 4 12.5H8.92593C8.84617 12.2884 8.70076 12.0616 8.48359 11.8313C7.2806 10.5553 6.5 8.70712 6.5 7ZM12 3.5C10.067 3.5 8.5 5.067 8.5 7C8.5 8.15682 9.05711 9.52411 9.9388 10.4593C10.485 11.0386 11 11.8708 11 12.8762V13C11 13.8284 10.3284 14.5 9.5 14.5H4V17.5H20V14.5H14.5C13.6716 14.5 13 13.8284 13 13V12.8762C13 11.8708 13.515 11.0386 14.0612 10.4593C14.9429 9.5241 15.5 8.15682 15.5 7C15.5 5.067 13.933 3.5 12 3.5ZM2 20.5001H22V22.5001H2V20.5001Z",fillOpacity:.9}}]},g=v({name:"SealIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-seal",l.value]),u=a(()=>s(s({},c.value),t.style)),C=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>y(m,C.value)}});export{g as default};
