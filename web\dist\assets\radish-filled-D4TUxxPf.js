import{d,h as a,ab as L,ac as O,ad as y}from"./index-D24a7sNI.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16.3892 6.1965L16.3892 1.95386H14.3892L14.3892 6.45436C13.5742 5.84268 12.458 5.12066 11.4068 4.90282C10.0339 4.56554 8.44594 4.7826 6.9806 5.52419L10.5608 9.15622L9.13641 10.5602L5.29779 6.66598C3.9776 7.82153 2.86671 9.5036 2.1875 11.7411L6.56079 16.1521L5.14053 17.5603L1.67477 14.0646C1.3732 16.1255 1.38663 18.5434 1.81945 21.3449L1.93127 22.0686L2.65504 22.1805C7.84793 22.9828 11.7428 22.3302 14.4697 20.9013L10.378 16.788L11.796 15.3775L16.1825 19.7873C18.7215 17.7498 19.6657 14.9054 19.0971 12.5931C18.8792 11.5418 18.1572 10.4257 17.5456 9.61071L22.046 9.61071V7.61071L17.8034 7.61071L21.4143 3.99979L20.0001 2.58557L16.3892 6.1965Z"}}]},m=d({name:"RadishFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=L(t),p=a(()=>["t-icon","t-icon-radish-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(C,f.value)}});export{m as default};
