import{d as v,h as a,ab as g,ac as O,ad as h}from"./index-D24a7sNI.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){h(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M5 6.4998V17.4998L1 17.5 1 6.5 5 6.4998zM7 18.2099L15.0004 22.7095 15.0004 1.29004 7 5.7896V18.2099zM21.1569 6.34285L20.4497 5.63574 19.0355 7.04996 19.7426 7.75706C22.0858 10.1002 22.0858 13.8992 19.7426 16.2423L19.0355 16.9495 20.4497 18.3637 21.1569 17.6566C24.2811 14.5324 24.2811 9.46704 21.1569 6.34285z"}},{tag:"path",attrs:{fill:"currentColor",d:"M18.682 7.40351L17.2678 8.81772L17.9749 9.52483C19.3417 10.8917 19.3417 13.1077 17.9749 14.4746L17.2678 15.1817L18.682 16.5959L19.3891 15.8888C21.537 13.7409 21.537 10.2585 19.3891 8.11062L18.682 7.40351Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M17.6213 14.121C18.7929 12.9495 18.7929 11.05 17.6213 9.87838L16.9142 9.17128L15.5 10.5855L16.2071 11.2926C16.5976 11.6831 16.5976 12.3163 16.2071 12.7068L15.5 13.4139L16.9142 14.8281L17.6213 14.121Z"}}]},C=v({name:"SoundHighFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=g(r),p=a(()=>["t-icon","t-icon-sound-high-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:d=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:d})}}));return()=>O(y,f.value)}});export{C as default};
