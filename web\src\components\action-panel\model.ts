export interface FlowInfo {
  id: string;
  version: string | number;
  middleware?: string[];
  data: FlowData[];
  persistence?: boolean;
  body: FlowStep[];
}

export interface FlowDataValue {
  type: 'text' | 'variable' | 'script' | 'visual';
  dataType?: string;
  textValue?: string;
  variableType?: 'current' | 'local' | 'global' | '';
  variableValue?: string;
  variableName?: string;
  scriptValue?: string;
  scriptName?: string;
  visualSteps?: VisualFunctionStep[];
  visualName?: string;
  // 新增：变量引用ID，用于实时解析变量信息
  variableRefId?: string;
  // 新增：变量引用路径，用于实时解析变量信息
  variableRefPath?: string;
}

// 新增：变量引用信息接口
export interface VariableReference {
  id: string;
  path: string;
  type: string;
  name: string;
  description?: string;
  source: 'current' | 'local' | 'global';
}

export interface VisualFunctionStep {
  id: string;
  functionName: string;
  functionType: 'csharp' | 'javascript' | 'builtin';
  displayName: string;
  description?: string;
  parameters: VisualFunctionParameter[];
  outputVariable?: string;
  order: number;
}

export interface VisualFunctionParameter {
  name: string;
  type: 'text' | 'variable' | 'previousResult';
  value: any;
  required: boolean;
  description?: string;
}

export interface VariableItem {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  source: 'temp' | 'local' | 'global';
  value: any;
  description?: string;
}

export interface FlowData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  required?: boolean;
  isCustomize?: boolean;
  value?: FlowDataValue;
  children?: FlowData[];
}

export interface FlowStep {
  id: string;
  name: string;
  description?: string;
  function: string;
  args?: any;
  nextId?: string;
  result?: FlowData[];
  undoFunction?: string;
  middleware?: string[];
  wait?: boolean;
  controlType?: string;
  control?: FlowControl;
  behavior?: any;
  config?: ActionConfig;
}
export interface FlowControl {
  branch?: {
    hasData: {
      nextId: string;
      condition?: string;
    };
    noData?: {
      nextId: string;
      condition?: string;
    };
  };
  forEach?: {
    async?: boolean;
    list?: string;
    item: FlowData[];
    nextId: string;
  };
  while?: {
    loopType: string;
    loopCount?: FlowDataValue;
    condition?: string;
    nextId: string;
  };
  transaction?: {
    rollbackType: string;
    undoToPerStep: boolean;
    nextId: string;
  };
  tryCatch?: {
    nextId: string;
    allowRetry: boolean;
    retryCount?: number;
    retryDelaysInSeconds?: number;
  };
}

export interface ActionConfig {
  name: string;
  function: string;
  controlType?: string;
  endStepName?: string;
  icon?: string;
  componentName?: string;
}

export interface ConditionInfo {
  id: string;
  type: 'node' | 'leaf';
  operator:
    | 'AND'
    | 'OR'
    | 'Equals'
    | 'NotEquals'
    | 'GreaterThan'
    | 'LessThan'
    | 'GreaterThanOrEqual'
    | 'LessThanOrEqual'
    | 'In'
    | 'NotIn'
    | 'Like'
    | 'NotLike'
    | 'LeftLike'
    | 'RightLike';
  column?: string;
  columnValue?: FlowDataValue;
  value?: FlowDataValue;
  children?: ConditionInfo[];
  isFilter?: boolean;
}

export interface ColumnInfo {
  columnName: string;
  columnValue?: any;
  description: string;
  dataType: string;
  required: boolean;
  isCustomize: boolean;
  isQuery?: boolean;
  isUpdate?: boolean;
  isInsert?: boolean;
  isCondition?: boolean;
  isPrimaryKey?: boolean;
}

export interface SortColumnInfo {
  id: string;
  columnName: string;
  order: string;
}

export interface DataSourceTableData {
  tableName: string;
  tableDescription: string;
  columns: ColumnInfo[];
  sortColumns: SortColumnInfo[];
  conditions: ConditionInfo;
}
