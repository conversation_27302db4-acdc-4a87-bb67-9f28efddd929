import{d as T,r as l,j,i as q,m as x,S as y,f as s,c as C,o as f,g as t,a as v,n as a,p as c,u as h,q as z,x as F,F as G,s as H,b as O,t as _,M as k,O as R,_ as $}from"./index-D24a7sNI.js";import J from"./addModal-D3dJkBU0.js";import{_ as K}from"./uploadModal.vue_vue_type_script_setup_true_lang-DX-kvOgi.js";import Q from"./more-BmZT2UIc.js";const W={class:"search-input"},X={class:"card-container"},Y={class:"card-title"},Z={name:"PublishConfig"},ee=T({...Z,setup(te){const g=l([]),i=l(!1),m=l(!1),u=l("新建发布环境"),d=l({environmentName:"",environmentType:"",serviceAddress:"",state:1,serviceSecret:"",serviceId:"",tag:"",description:""}),b=l(""),w=[{content:"复制",value:1},{content:"编辑",value:2},{content:"备份",value:3},{content:"删除",value:4}],B=(o,e)=>{o.value===4?A(e.id):o.value===1?(i.value=!0,u.value="复制发布环境",d.value={...e}):o.value===2?(i.value=!0,u.value="编辑发布环境",d.value={...e}):k.info("备份")};j(()=>{r()}),q(()=>{r()});const D=()=>{i.value=!0,d.value={environmentName:"",environmentType:"",serviceAddress:"",state:1,serviceSecret:"",serviceId:"",tag:"",description:""},u.value="新建发布环境"},V=()=>{m.value=!0},A=o=>{const e=R({header:"确认删除该环境吗？",confirmBtn:"确定",cancelBtn:"取消",onConfirm:()=>{k.success("删除成功"),e.hide(),x.run(y.publishDelete,{id:o}).then(()=>{r()})},onClose:()=>{e.destroy()}})},r=async()=>{await x.run(y.publishGetAll).then(o=>{g.value=o})};return(o,e)=>{const p=s("t-button"),M=s("t-input"),N=s("t-row"),U=s("t-avatar"),S=s("t-avatar-group"),E=s("t-dropdown"),I=s("t-card");return f(),C("div",null,[t(N,{justify:"space-between",class:"top-bar"},{default:a(()=>[v("div",null,[t(p,{onClick:D},{default:a(()=>e[3]||(e[3]=[c(" 新建发布环境 ",-1)])),_:1,__:[3]}),t(p,{onClick:V},{default:a(()=>e[4]||(e[4]=[c(" 导入当前环境 ",-1)])),_:1,__:[4]})]),v("div",W,[t(M,{modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=n=>b.value=n),placeholder:h(z)("pages.listBase.placeholder"),clearable:"",onEnter:r},{"suffix-icon":a(()=>[t(h(F),{size:"16px"})]),_:1},8,["modelValue","placeholder"])])]),_:1}),v("div",X,[(f(!0),C(G,null,H(g.value,(n,L)=>(f(),O(I,{key:L,theme:"poster2",style:{width:"24%",marginBottom:"16px",marginRight:"11px"}},{header:a(()=>[v("span",Y,_(n.environmentName),1),t(p,{theme:n.state==1?"success":"default",size:"small"},{default:a(()=>[c(_(n.state===1?"已启用":"已停用"),1)]),_:2},1032,["theme"])]),footer:a(()=>[t(S,{cascading:"left-up",max:2},{default:a(()=>[t(U,null,{default:a(()=>[c(_(n.tag),1)]),_:2},1024)]),_:2},1024)]),actions:a(()=>[t(E,{options:w,"min-column-width":60,onClick:P=>B(P,n)},{default:a(()=>[t(p,{variant:"text",shape:"square"},{default:a(()=>[t(h(Q))]),_:1})]),_:2},1032,["onClick"])]),default:a(()=>[c(" "+_(n.description)+" ",1)]),_:2},1024))),128))]),t(J,{visible:i.value,"onUpdate:visible":e[1]||(e[1]=n=>i.value=n),title:u.value,"copy-data":d.value,onUpdataList:r},null,8,["visible","title","copy-data"]),t(K,{visible:m.value,"onUpdate:visible":e[2]||(e[2]=n=>m.value=n)},null,8,["visible"])])}}}),le=$(ee,[["__scopeId","data-v-1ee8fd52"]]);export{le as default};
