import{d as C,h as a,ab as O,ac as y,ad as d}from"./index-D24a7sNI.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.0002 3.12602V1H13.0002V3.12602C14.7254 3.57006 16.0002 5.13616 16.0002 7C16.0002 8.22945 15.4452 9.32849 14.5748 10.0613L16.1274 23H7.87305L9.42569 10.0613C8.55527 9.32849 8.00022 8.22945 8.00022 7C8.00022 5.13616 9.27499 3.57006 11.0002 3.12602ZM11.334 10.9446L10.1274 21H13.873L12.6664 10.9446C12.4495 10.981 12.2269 11 12.0002 11C11.7735 11 11.5509 10.981 11.334 10.9446ZM12.0002 5C10.8957 5 10.0002 5.89543 10.0002 7C10.0002 7.73599 10.3972 8.38039 10.9936 8.72876C11.2884 8.901 11.6312 9 12.0002 9C12.3692 9 12.712 8.901 13.0069 8.72876C13.6032 8.38039 14.0002 7.73599 14.0002 7C14.0002 5.89543 13.1048 5 12.0002 5Z"}}]},g=C({name:"Tower3Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-tower-3",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
